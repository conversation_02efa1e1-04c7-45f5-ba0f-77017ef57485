// 后台服务，用于管理扩展状态
chrome.runtime.onInstalled.addListener(() => {
    // 设置默认值
    chrome.storage.local.set({
        autoAnswer: true,
        autoPlay: true,
        playbackRate: 1,
        autoSkipVideo: false
    });
});

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // 新增考试结果记录处理
    if (message.action === "recordExamResults") {
        chrome.storage.local.get("JJ_ExamResults", (result) => {
            let examResults = result.JJ_ExamResults ? JSON.parse(result.JJ_ExamResults) : {};
            // 更新考试结果 - 合并新的结果
            if (message.results && typeof message.results === 'object') {
                // 合并整个结果对象
                examResults = { ...examResults, ...message.results };
            } else if (message.question && message.answer) {
                // 兼容旧版本的单个问题/答案格式
                examResults[message.question] = message.answer;
            }
            
            chrome.storage.local.set({ "JJ_ExamResults": JSON.stringify(examResults) }, () => {
                sendResponse({ success: true });
            });
        });
        return true; // 表示异步响应
    }
    
    if (message.action === "getAnswerStats") {
        // 获取答案统计信息
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (!tabs || tabs.length === 0) {
                sendResponse({ totalAnswers: 0, chapterCount: 0 });
                return;
            }
            
            chrome.scripting.executeScript({
                target: {tabId: tabs[0].id},
                function: () => {
                    const qAllAnswer = JSON.parse(localStorage.getItem("JJ_AllAnswer")) || {};
                    
                    let totalAnswers = 0;
                    let chapterCount = 0;
                    
                    for (const chapter in qAllAnswer) {
                        if (qAllAnswer[chapter] && typeof qAllAnswer[chapter] === 'object') {
                            chapterCount++;
                            totalAnswers += Object.keys(qAllAnswer[chapter]).length;
                        }
                    }
                    
                    return { totalAnswers, chapterCount };
                }
            }, (results) => {
                if (chrome.runtime.lastError || !results || !results[0]) {
                    sendResponse({ totalAnswers: 0, chapterCount: 0 });
                } else {
                    sendResponse(results[0].result);
                }
            });
        });
        return true; // 表示将异步发送响应
    }
});
