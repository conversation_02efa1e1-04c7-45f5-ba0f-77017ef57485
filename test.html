<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华医网视频跳过功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            margin: 20px 0;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 600px;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 4px;
        }
        .button-container {
            margin: 20px 0;
            text-align: center;
        }
        .exam-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 10px;
        }
        .exam-btn:hover {
            background-color: #0056b3;
        }
        .exam-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log-container {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background-color: white;
        }
        .popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 1000;
        }
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>华医网视频跳过功能测试页面</h1>
        
        <div class="video-container">
            <h3>测试视频</h3>
            <video id="testVideo" controls>
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                您的浏览器不支持视频标签。
            </video>
            <p>视频时长: <span id="videoDuration">加载中...</span></p>
            <p>当前时间: <span id="currentTime">0</span></p>
        </div>
        
        <div class="button-container">
            <h3>考试按钮测试</h3>
            <input type="button" value="进入考试" class="exam-btn" onclick="handleExamClick('input按钮')">
            <button class="exam-btn" onclick="handleExamClick('button元素')">开始考试</button>
            <a href="#" class="exam-btn" onclick="handleExamClick('链接元素'); return false;">考试</a>
            <input type="button" value="测试" class="exam-btn" disabled id="disabledBtn">
        </div>
        
        <div class="button-container">
            <h3>功能测试按钮</h3>
            <button onclick="testSkipVideo()" class="exam-btn">测试跳过视频</button>
            <button onclick="testFindExamButton()" class="exam-btn">测试查找考试按钮</button>
            <button onclick="testAutoSetup()" class="exam-btn">测试自动设置</button>
            <button onclick="showTimeWarning()" class="exam-btn">模拟时间警告</button>
        </div>
        
        <div class="log-container">
            <h3>测试日志</h3>
            <div id="logOutput"></div>
        </div>
    </div>
    
    <!-- 模拟弹窗 -->
    <div class="popup-overlay" id="popupOverlay"></div>
    <div class="popup" id="timeWarningPopup">
        <h3>提示</h3>
        <p>视频观看时间不足，请重新观看！</p>
        <button onclick="closePopup()" class="exam-btn">确定</button>
    </div>
    
    <script>
        // 模拟全局变量
        let autoSkipVideo = true;
        let autoPlay = true;
        let vSpeed = 2.0;
        
        // 日志函数
        function addLog(message) {
            const logOutput = document.getElementById('logOutput');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }
        
        // 视频事件监听
        const video = document.getElementById('testVideo');
        video.addEventListener('loadedmetadata', function() {
            document.getElementById('videoDuration').textContent = video.duration.toFixed(2) + '秒';
        });
        
        video.addEventListener('timeupdate', function() {
            document.getElementById('currentTime').textContent = video.currentTime.toFixed(2) + '秒';
        });
        
        // 考试按钮点击处理
        function handleExamClick(buttonType) {
            addLog(`点击了${buttonType}，模拟进入考试页面`);
            alert(`成功点击${buttonType}！在实际环境中会跳转到考试页面。`);
        }
        
        // 测试函数
        function testSkipVideo() {
            addLog('开始测试跳过视频功能');
            if (typeof skipVideo === 'function') {
                skipVideo();
            } else {
                addLog('skipVideo函数未定义，请确保content.js已加载');
            }
        }
        
        function testFindExamButton() {
            addLog('开始测试查找考试按钮功能');
            if (typeof findExamButton === 'function') {
                const button = findExamButton();
                if (button) {
                    addLog(`找到考试按钮: ${button.tagName} - ${button.textContent || button.value}`);
                } else {
                    addLog('未找到考试按钮');
                }
            } else {
                addLog('findExamButton函数未定义，请确保content.js已加载');
            }
        }
        
        function testAutoSetup() {
            addLog('开始测试自动设置功能');
            if (typeof setupAutoSkipVideo === 'function') {
                setupAutoSkipVideo();
            } else {
                addLog('setupAutoSkipVideo函数未定义，请确保content.js已加载');
            }
        }
        
        function showTimeWarning() {
            addLog('显示时间警告弹窗');
            document.getElementById('popupOverlay').style.display = 'block';
            document.getElementById('timeWarningPopup').style.display = 'block';
        }
        
        function closePopup() {
            addLog('关闭弹窗');
            document.getElementById('popupOverlay').style.display = 'none';
            document.getElementById('timeWarningPopup').style.display = 'none';
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            addLog('测试页面加载完成');
            
            // 5秒后启用禁用的按钮
            setTimeout(function() {
                const disabledBtn = document.getElementById('disabledBtn');
                disabledBtn.disabled = false;
                disabledBtn.style.backgroundColor = '#28a745';
                addLog('禁用的测试按钮已启用');
            }, 5000);
        });
    </script>
</body>
</html>
