body {
    font-family: 'Microsoft YaHei', sans-serif;
    width: 350px;
    margin: 0;
    padding: 0;
    background-color: #f5f7fa;
    color: #333;
}

.container {
    padding: 15px;
}

.header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #e0e6ed;
    padding-bottom: 15px;
}

.logo {
    width: 40px;
    height: 40px;
    margin-right: 12px;
}

h1 {
    font-size: 18px;
    margin: 0;
    color: #2c3e50;
}

.control-panel {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.control-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.control-label {
    margin-left: 10px;
    font-size: 14px;
}

.info-row {
    display: flex;
    align-items: center;
    margin-top: 15px;
    font-size: 14px;
}

.info-row label {
    margin-right: 10px;
}

#speedControl {
    flex: 1;
    margin-right: 10px;
}

#speedValue {
    width: 30px;
    text-align: center;
}

.button-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.action-btn {
    padding: 10px;
    border: none;
    border-radius: 5px;
    background-color: #4cb0f9;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;
    text-align: center;
}

.action-btn:hover {
    background-color: #3a9ee0;
}

.danger {
    background-color: #f15854;
}

.danger:hover {
    background-color: #e04a46;
}

.stats {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stats h3 {
    margin-top: 0;
    margin-bottom: 12px;
    font-size: 16px;
    color: #2c3e50;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin-bottom: 8px;
}

.status-bar {
    font-size: 12px;
    color: #7f8c8d;
    text-align: center;
    padding: 8px;
    background-color: #f0f4f8;
    border-radius: 5px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4cb0f9;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

input:focus + .slider {
    box-shadow: 0 0 1px #4cb0f9;
}