document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const autoAnswerToggle = document.getElementById('autoAnswerToggle');
    const autoPlayToggle = document.getElementById('autoPlayToggle');
    const autoSkipToggle = document.getElementById('autoSkipToggle');
    const speedControl = document.getElementById('speedControl');
    const speedValue = document.getElementById('speedValue');
    const showAnswersBtn = document.getElementById('showAnswersBtn');
    const clearAnswersBtn = document.getElementById('clearAnswersBtn');
    const answerCount = document.getElementById('answerCount');
    const chapterCount = document.getElementById('chapterCount');
    const statusText = document.getElementById('statusText');

    // 从存储加载设置
    chrome.storage.local.get(['autoAnswer', 'autoPlay', 'playbackRate', 'autoSkipVideo'], function(result) {
        autoAnswerToggle.checked = result.autoAnswer !== undefined ? result.autoAnswer : true;
        autoPlayToggle.checked = result.autoPlay !== undefined ? result.autoPlay : true;
        autoSkipToggle.checked = result.autoSkipVideo !== undefined ? result.autoSkipVideo : false;
        speedControl.value = result.playbackRate || 1;
        speedValue.textContent = speedControl.value + 'x';
    });

    // 更新统计数据
    updateStats();

    // 自动答题开关事件
    autoAnswerToggle.addEventListener('change', function() {
        chrome.storage.local.set({autoAnswer: this.checked}, function() {
            statusText.textContent = this.checked ? 
                "自动答题功能已开启" : "自动答题功能已关闭";
            
            // 通知内容脚本
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.tabs.sendMessage(tabs[0].id, {
                    action: "autoAnswerChange",
                    autoAnswer: this.checked
                });
            }.bind(this));
        }.bind(this));
    });

    // 自动下一课开关事件
    autoPlayToggle.addEventListener('change', function() {
        chrome.storage.local.set({autoPlay: this.checked}, function() {
            statusText.textContent = this.checked ? 
                "自动下一课功能已开启" : "自动下一课功能已关闭";
        });
    });

    // 自动跳过视频开关事件
    autoSkipToggle.addEventListener('change', function() {
        chrome.storage.local.set({autoSkipVideo: this.checked}, function() {
            statusText.textContent = this.checked ? 
                "自动跳过视频已开启" : "自动跳过视频已关闭";
        });
    });

    // 播放速度控制事件
    speedControl.addEventListener('input', function() {
        const speed = this.value;
        speedValue.textContent = speed + 'x';
        chrome.storage.local.set({playbackRate: speed});
    });

    // 显示答案按钮事件
    showAnswersBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs.length === 0) return;
            
            chrome.tabs.sendMessage(tabs[0].id, {action: "showAnswers"}, function(response) {
                if (chrome.runtime.lastError) {
                    statusText.textContent = "请刷新华医网页面后再试";
                } else {
                    statusText.textContent = "已显示答案";
                }
            });
        });
    });

    // 清除答案按钮事件
    clearAnswersBtn.addEventListener('click', function() {
        if (confirm("确定要清除所有记录的答案吗？此操作不可撤销！")) {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                if (tabs.length === 0) return;
                
                chrome.tabs.sendMessage(tabs[0].id, {action: "clearAnswers"}, function() {
                    if (chrome.runtime.lastError) {
                        statusText.textContent = "清除失败，请确保在华医网页面";
                    } else {
                        statusText.textContent = "已清除所有答案记录";
                        updateStats();
                    }
                });
            });
        }
    });

    // 更新统计数据
    function updateStats() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs.length === 0) {
                answerCount.textContent = "0";
                chapterCount.textContent = "0";
                return;
            }
            
            chrome.tabs.sendMessage(tabs[0].id, {action: "getAnswerStats"}, function(response) {
                if (chrome.runtime.lastError || !response) {
                    answerCount.textContent = "0";
                    chapterCount.textContent = "0";
                } else {
                    answerCount.textContent = response.totalAnswers;
                    chapterCount.textContent = response.chapterCount;
                }
            });
        });
    }
});
